import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ZoomManager {
  // Zoom-related properties
  final Rx<double> zoomScale = 1.0.obs;
  final Rx<Offset> panOffset = Offset.zero.obs;
  final RxBool isPanning = false.obs;

  // Reset zoom and pan to initial values
  void resetZoom() {
    zoomScale.value = 1.0;
    panOffset.value = Offset.zero;
  }

  // Update zoom scale and pan offset
  void updateZoom(double scale, Offset offset) {
    final originalScale = scale;
    // Limit zoom scale to extended bounds for detailed editing and broad overview
    // Minimum: 0.01x (1%) for extreme zoom out to see overall layout
    // Maximum: 50.0x (5000%) for extreme zoom in for precise detail work
    scale = scale.clamp(0.01, 50.0);

    // Debug print to track zoom clamping
    if (originalScale != scale) {
      debugPrint(
          '[ZoomManager] Zoom clamped from ${(originalScale * 100).toInt()}% to ${(scale * 100).toInt()}%');
    }

    zoomScale.value = scale;

    // Enforce bounds on the pan offset to ensure the user can reach the grid edges
    panOffset.value = offset;
  }

  // Update just the pan offset
  void updatePanOffset(Offset newOffset) {
    panOffset.value = newOffset;
  }

  // Apply a pan delta (for auto-panning)
  void applyPanDelta(Offset delta) {
    panOffset.value = panOffset.value + delta;
  }

  // Calculate pan bounds based on current screen size, zoom level and grid size
  Offset calculatePanBounds(
      Size screenSize, Offset panOffset, double zoom, bool isMin) {
    // For completely free panning, we'll use a very large number
    // This effectively removes any practical limits to how far you can pan
    const veryLargeNumber = 100000.0;

    if (isMin) {
      // For minimum bounds (how far left/up you can pan)
      return Offset(-veryLargeNumber, -veryLargeNumber);
    } else {
      // For maximum bounds (how far right/down you can pan)
      return Offset(veryLargeNumber, veryLargeNumber);
    }
  }
}
